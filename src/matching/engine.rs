use crate::account::AccountManager;
use crate::config::PlaybackConfig;
use crate::matching::OrderBook;
use crate::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price, Trade};
use crate::{BacktestError, Result};
use std::collections::{BinaryHeap, HashMap};
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc, Mutex};
use tokio::time::{sleep, Duration, Instant};
use tracing::{debug, error, info, warn};

/// 时间同步的市场数据项
#[derive(Debug, Clone)]
struct TimestampedMarketData {
    data: MarketData,
    timestamp: u64,
}

impl PartialEq for TimestampedMarketData {
    fn eq(&self, other: &Self) -> bool {
        self.timestamp == other.timestamp
    }
}

impl Eq for TimestampedMarketData {}

impl PartialOrd for TimestampedMarketData {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for TimestampedMarketData {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        // 注意：BinaryHeap 是最大堆，我们需要最小堆，所以反转比较
        other.timestamp.cmp(&self.timestamp)
    }
}

/// 撮合引擎
pub struct MatchingEngine {
    /// 订单簿
    orderbook: OrderBook,
    /// 待处理订单
    pending_orders: HashMap<String, Order>,
    /// 账户管理器
    account_manager: Arc<Mutex<AccountManager>>,
    /// 市场数据输入
    market_data_rx: broadcast::Receiver<MarketData>,
    /// 订单输入
    order_rx: mpsc::Receiver<Order>,
    /// 成交输出
    trade_tx: broadcast::Sender<Trade>,
    /// 订单状态更新输出
    order_update_tx: broadcast::Sender<Order>,
    /// 市场数据转发输出（给WebSocket服务器）
    market_data_forward_tx: broadcast::Sender<MarketData>,
    /// 当前市场价格缓存
    current_prices: HashMap<String, f64>,
    /// 时间同步缓冲区（优先队列，按时间戳排序）
    time_sync_buffer: BinaryHeap<TimestampedMarketData>,
    /// 缓冲区大小限制
    buffer_size_limit: usize,
    /// 时间窗口（微秒），用于决定何时处理缓冲区中的数据
    time_window_micros: u64,
    /// 回放配置
    playback_config: PlaybackConfig,
    /// 回放速率控制相关字段
    last_batch_time: Option<Instant>,
    processed_in_current_second: u32,
    current_second_start: Option<Instant>,
}

impl MatchingEngine {
    /// 创建新的撮合引擎
    pub fn new(
        account_manager: Arc<Mutex<AccountManager>>,
        market_data_rx: broadcast::Receiver<MarketData>,
        order_rx: mpsc::Receiver<Order>,
        trade_tx: broadcast::Sender<Trade>,
        order_update_tx: broadcast::Sender<Order>,
        market_data_forward_tx: broadcast::Sender<MarketData>,
    ) -> Self {
        Self {
            orderbook: OrderBook::new(),
            pending_orders: HashMap::new(),
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
            current_prices: HashMap::new(),
            time_sync_buffer: BinaryHeap::new(),
            buffer_size_limit: 1000,     // 默认缓冲区大小
            time_window_micros: 100_000, // 100ms 时间窗口
            playback_config: PlaybackConfig::default(),
            last_batch_time: None,
            processed_in_current_second: 0,
            current_second_start: None,
        }
    }

    /// 创建新的撮合引擎（带回放配置）
    pub fn new_with_playback_config(
        account_manager: Arc<Mutex<AccountManager>>,
        market_data_rx: broadcast::Receiver<MarketData>,
        order_rx: mpsc::Receiver<Order>,
        trade_tx: broadcast::Sender<Trade>,
        order_update_tx: broadcast::Sender<Order>,
        market_data_forward_tx: broadcast::Sender<MarketData>,
        playback_config: PlaybackConfig,
    ) -> Self {
        Self {
            orderbook: OrderBook::new(),
            pending_orders: HashMap::new(),
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
            current_prices: HashMap::new(),
            time_sync_buffer: BinaryHeap::new(),
            buffer_size_limit: 1000,     // 默认缓冲区大小
            time_window_micros: 100_000, // 100ms 时间窗口
            playback_config,
            last_batch_time: None,
            processed_in_current_second: 0,
            current_second_start: None,
        }
    }

    /// 启动撮合引擎
    pub async fn start(&mut self) -> Result<()> {
        info!("Starting matching engine");

        // 检查订单通道是否已关闭
        let mut order_channel_closed = false;

        loop {
            tokio::select! {
                // 处理市场数据
                market_data = self.market_data_rx.recv() => {
                    match market_data {
                        Ok(data) => {
                            if let Err(e) = self.process_market_data(data).await {
                                error!("Failed to process market data: {}", e);
                            }
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Market data channel closed");
                            break;
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            debug!("Market data lagged, skipped {} messages", skipped);
                        }
                    }
                }

                // 处理新订单（如果通道还开着）
                order = self.order_rx.recv(), if !order_channel_closed => {
                    match order {
                        Some(order) => {
                            if let Err(e) = self.process_order(order).await {
                                error!("Failed to process order: {}", e);
                            }
                        }
                        None => {
                            info!("Order channel closed");
                            order_channel_closed = true;
                            // 不退出循环，继续处理市场数据
                        }
                    }
                }
            }
        }

        // 在停止前处理缓冲区中剩余的数据
        info!("Processing remaining buffered data before stopping");
        self.process_buffered_data().await?;

        info!("Matching engine stopped");
        Ok(())
    }

    /// 处理市场数据
    async fn process_market_data(&mut self, market_data: MarketData) -> Result<()> {
        debug!("Received market data in matching engine: {:?}", market_data);

        // 将市场数据添加到时间同步缓冲区
        let timestamp = market_data.timestamp_for_sorting();
        let timestamped_data = TimestampedMarketData {
            data: market_data,
            timestamp,
        };

        self.time_sync_buffer.push(timestamped_data);
        debug!(
            "Added data to buffer, buffer size: {}",
            self.time_sync_buffer.len()
        );

        // 检查缓冲区大小，防止内存溢出
        if self.time_sync_buffer.len() > self.buffer_size_limit {
            warn!("Time sync buffer size exceeded limit, processing oldest data");
            self.process_buffered_data().await?;
        }

        // 检查是否应该处理缓冲区中的数据
        self.check_and_process_buffer().await?;

        Ok(())
    }

    /// 直接添加数据到缓冲区而不处理（用于测试）
    #[cfg(test)]
    pub async fn add_to_buffer_only(&mut self, market_data: MarketData) -> Result<()> {
        let timestamp = market_data.timestamp_for_sorting();
        let timestamped_data = TimestampedMarketData {
            data: market_data,
            timestamp,
        };

        self.time_sync_buffer.push(timestamped_data);
        Ok(())
    }

    /// 检查并处理缓冲区中的数据
    async fn check_and_process_buffer(&mut self) -> Result<()> {
        if self.time_sync_buffer.is_empty() {
            return Ok(());
        }

        // 获取当前最新的时间戳
        let current_time = chrono::Utc::now().timestamp_micros() as u64;

        // 处理所有超过时间窗口的数据
        while let Some(oldest_data) = self.time_sync_buffer.peek() {
            // 如果最老的数据超过了时间窗口，就处理它
            if current_time.saturating_sub(oldest_data.timestamp) > self.time_window_micros {
                let data = self.time_sync_buffer.pop().unwrap();
                self.process_single_market_data_internal(data.data).await?;
            } else {
                // 如果最老的数据还在时间窗口内，就停止处理
                break;
            }
        }

        Ok(())
    }

    /// 设置时间窗口（用于测试）
    #[cfg(test)]
    pub fn set_time_window_micros(&mut self, window: u64) {
        self.time_window_micros = window;
    }

    /// 检查并应用回放速率控制
    async fn apply_playback_rate_control_internal(&mut self) -> Result<()> {
        if !self.playback_config.enabled || self.playback_config.rate_per_second == 0 {
            // 如果未启用速率控制或速率为0（无限制），直接返回
            return Ok(());
        }

        let now = Instant::now();

        // 初始化或重置计数器
        if let Some(second_start) = self.current_second_start {
            if now.duration_since(second_start) >= Duration::from_secs(1) {
                // 新的一秒开始，重置计数器
                self.current_second_start = Some(now);
                self.processed_in_current_second = 0;
            }
        } else {
            // 第一次运行，初始化
            self.current_second_start = Some(now);
            self.processed_in_current_second = 0;
        }

        // 检查是否超过了当前秒的处理限制
        if self.processed_in_current_second >= self.playback_config.rate_per_second {
            // 计算需要等待的时间
            let second_start = self.current_second_start.unwrap();
            let elapsed = now.duration_since(second_start);
            if elapsed < Duration::from_secs(1) {
                let wait_time = Duration::from_secs(1) - elapsed;
                debug!("Rate limit reached, waiting {:?}", wait_time);
                sleep(wait_time).await;

                // 等待后重置计数器
                self.current_second_start = Some(Instant::now());
                self.processed_in_current_second = 0;
            }
        }

        Ok(())
    }

    /// 更新回放配置
    pub fn update_playback_config(&mut self, config: PlaybackConfig) {
        info!(
            "Updating playback config: rate_per_second={}, enabled={}, batch_size={}",
            config.rate_per_second, config.enabled, config.batch_size
        );
        self.playback_config = config;
        // 重置速率控制状态
        self.current_second_start = None;
        self.processed_in_current_second = 0;
    }

    /// 获取当前回放配置
    pub fn get_playback_config(&self) -> &PlaybackConfig {
        &self.playback_config
    }

    /// 公开缓冲区访问（用于演示和测试）
    pub fn time_sync_buffer(&mut self) -> &mut BinaryHeap<TimestampedMarketData> {
        &mut self.time_sync_buffer
    }

    /// 公开单个数据处理方法（用于演示）
    pub async fn process_single_market_data(&mut self, market_data: MarketData) -> Result<()> {
        self.process_single_market_data_internal(market_data).await
    }

    /// 公开速率控制方法（用于演示）
    pub async fn apply_playback_rate_control(&mut self) -> Result<()> {
        self.apply_playback_rate_control_internal().await
    }

    /// 公开处理计数器访问（用于演示）
    pub fn processed_in_current_second(&mut self) -> &mut u32 {
        &mut self.processed_in_current_second
    }

    /// 强制处理缓冲区中的所有数据
    pub async fn process_buffered_data(&mut self) -> Result<()> {
        while let Some(timestamped_data) = self.time_sync_buffer.pop() {
            self.process_single_market_data_internal(timestamped_data.data)
                .await?;
        }
        Ok(())
    }

    /// 处理单个市场数据项
    async fn process_single_market_data_internal(&mut self, market_data: MarketData) -> Result<()> {
        info!("🔄 Processing market data: {:?}", market_data);

        // 在处理数据前先检查速率控制
        self.apply_playback_rate_control_internal().await?;

        // 更新账户管理器的价格信息
        self.update_account_prices(&market_data).await;

        // 先处理撮合逻辑
        match &market_data {
            MarketData::OrderBook(snapshot) => {
                debug!("Processing orderbook snapshot");
                // 重建订单簿
                self.orderbook
                    .rebuild_from_snapshot(&snapshot.bids, &snapshot.asks);
                // 尝试撮合待处理订单
                self.match_pending_orders().await?;
            }
            MarketData::Bbo(bbo) => {
                debug!("Processing BBO update with timestamp: {:?}", bbo.timestamp);
                // 使用BBO进行撮合
                self.match_with_bbo(bbo).await?;
            }
            MarketData::Trade(trade) => {
                debug!("Processing external trade: {}", trade.id);
                // 外部交易可能影响订单簿状态
                // 这里可以添加相应的处理逻辑
            }
            MarketData::BookTicker(bookticker) => {
                debug!(
                    "Processing BookTicker data: update_id={}",
                    bookticker.update_id
                );
                // 将BookTicker转换为BBO进行撮合
                let bbo = bookticker.to_bbo();
                self.match_with_bbo(&bbo).await?;
            }

            MarketData::TradeData(trade_data) => {
                debug!(
                    "Processing TradeData: id={} at timestamp={}",
                    trade_data.id, trade_data.timestamp
                );
                // 交易数据通常用于验证撮合结果，这里可以添加相关逻辑
            }
        }

        // 处理完撮合后，将市场数据转发给WebSocket服务器
        info!(
            "🚀 Matching engine forwarding market data to WebSocket: {:?}",
            market_data
        );
        match self.market_data_forward_tx.send(market_data) {
            Ok(receiver_count) => {
                info!(
                    "✅ Market data forwarded to {} WebSocket receivers",
                    receiver_count
                );
            }
            Err(e) => {
                info!("❌ Failed to forward market data to WebSocket: {}", e);
                // 这里不返回错误，因为WebSocket转发失败不应该影响撮合引擎的正常运行
            }
        }

        // 增加处理计数器
        self.processed_in_current_second += 1;

        Ok(())
    }

    /// 处理新订单
    async fn process_order(&mut self, mut order: Order) -> Result<()> {
        info!(
            "Processing order: {} {} {} @ {:?}",
            order.id,
            match order.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            order.quantity,
            order.price
        );

        match order.order_type {
            OrderType::Market => {
                // 市价单立即撮合
                self.match_market_order(&mut order).await?;
            }
            OrderType::Limit => {
                // 限价单先尝试撮合，未成交部分加入订单簿
                self.match_limit_order(&mut order).await?;
            }
        }

        Ok(())
    }

    /// 撮合市价单
    async fn match_market_order(&mut self, order: &mut Order) -> Result<()> {
        // 首先尝试与订单簿中的订单撮合
        let market_price = match order.side {
            OrderSide::Buy => {
                // 买入市价单设置极高价格
                if let Some(best_ask) = self.orderbook.best_ask() {
                    Price::new(best_ask.value() * 10.0) // 设置为最佳卖价的10倍
                } else {
                    Price::new(f64::MAX)
                }
            }
            OrderSide::Sell => {
                // 卖出市价单设置极低价格
                if let Some(best_bid) = self.orderbook.best_bid() {
                    Price::new(best_bid.value() * 0.1) // 设置为最佳买价的0.1倍
                } else {
                    Price::new(0.0)
                }
            }
        };

        // 临时设置市价单的价格用于撮合
        let mut market_order = order.clone();
        market_order.price = Some(market_price);

        // 使用订单簿的FIFO撮合功能
        match self.orderbook.match_order(&market_order) {
            Ok((matches, remaining_quantity)) => {
                info!(
                    "Orderbook match result: {} matches, remaining: {}",
                    matches.len(),
                    remaining_quantity
                );

                // 如果没有匹配，使用市场价格直接成交
                if matches.is_empty() {
                    let current_price = self
                        .get_current_market_price(&order.symbol)
                        .unwrap_or(70000.0); // 使用默认价格作为fallback

                    // 使用当前价格作为成交价格，不再添加额外的滑点
                    // 这样更符合实际情况，特别是对于小额订单
                    let trade_price = current_price;

                    // 直接以市场价格成交
                    let quantity = order.quantity; // 先保存数量，避免借用冲突
                    self.execute_trade(order, Price::new(trade_price), quantity)
                        .await?;
                    order.status = OrderStatus::Filled;
                    info!(
                        "Market order {} filled at market price {} (current: {:?})",
                        order.id,
                        trade_price,
                        self.get_current_market_price(&order.symbol)
                    );
                } else {
                    // 处理所有成交
                    for (matched_order, trade_price, trade_quantity) in matches {
                        self.execute_trade(order, trade_price, trade_quantity)
                            .await?;

                        // 更新被撮合订单的状态
                        let mut updated_order = matched_order;
                        if updated_order.quantity <= 0.0 {
                            updated_order.status = OrderStatus::Filled;
                        } else {
                            updated_order.status = OrderStatus::PartiallyFilled;
                        }
                        self.send_order_update(updated_order).await?;
                    }

                    // 更新原订单状态
                    if remaining_quantity <= 0.0 {
                        order.status = OrderStatus::Filled;
                    } else {
                        order.status = OrderStatus::PartiallyFilled;
                        order.quantity = remaining_quantity;
                    }
                }
            }
            Err(_e) => {
                // 如果订单簿撮合失败，尝试使用当前市场价格直接成交
                let current_price = self
                    .get_current_market_price(&order.symbol)
                    .unwrap_or(70000.0); // 使用默认价格作为fallback

                let trade_price = match order.side {
                    OrderSide::Buy => current_price * 1.001,  // 买入时稍微高一点
                    OrderSide::Sell => current_price * 0.999, // 卖出时稍微低一点
                };

                // 直接以市场价格成交
                let quantity = order.quantity; // 先保存数量，避免借用冲突
                self.execute_trade(order, Price::new(trade_price), quantity)
                    .await?;
                order.status = OrderStatus::Filled;
                info!(
                    "Market order {} filled at market price {} (current: {:?})",
                    order.id,
                    trade_price,
                    self.get_current_market_price(&order.symbol)
                );
            }
        }

        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 撮合限价单
    async fn match_limit_order(&mut self, order: &mut Order) -> Result<()> {
        let _order_price = order
            .price
            .ok_or_else(|| BacktestError::Matching("Limit order must have price".to_string()))?;

        // 使用订单簿的FIFO撮合功能
        match self.orderbook.match_order(order) {
            Ok((matches, remaining_quantity)) => {
                info!("Matched limit orders: {:?}", matches);
                // 处理所有成交
                for (matched_order, trade_price, trade_quantity) in matches {
                    self.execute_trade(order, trade_price, trade_quantity)
                        .await?;

                    // 更新被撮合订单的状态
                    let mut updated_order = matched_order;
                    if updated_order.quantity <= 0.0 {
                        updated_order.status = OrderStatus::Filled;
                        // 从pending_orders中移除已完成的订单
                        self.pending_orders.remove(&updated_order.id);
                    } else {
                        updated_order.status = OrderStatus::PartiallyFilled;
                        // 更新pending_orders中的订单
                        self.pending_orders
                            .insert(updated_order.id.clone(), updated_order.clone());
                    }
                    self.send_order_update(updated_order).await?;
                }

                // 更新原订单状态
                if remaining_quantity <= 0.0 {
                    order.status = OrderStatus::Filled;
                } else if remaining_quantity < order.quantity {
                    order.status = OrderStatus::PartiallyFilled;
                    order.quantity = remaining_quantity;
                    // 未成交部分加入订单簿和待处理订单
                    self.orderbook.add_order(order.clone());
                    self.pending_orders.insert(order.id.clone(), order.clone());
                } else {
                    // 完全未成交，直接加入订单簿和待处理订单
                    order.status = OrderStatus::Pending;
                    self.orderbook.add_order(order.clone());
                    self.pending_orders.insert(order.id.clone(), order.clone());
                }
            }
            Err(e) => {
                warn!("Failed to match limit order {}: {}", order.id, e);
                // 撮合失败，将订单加入订单簿
                order.status = OrderStatus::Pending;
                self.orderbook.add_order(order.clone());
                self.pending_orders.insert(order.id.clone(), order.clone());
            }
        }

        info!("Sending order update: {:?}", order);
        self.send_order_update(order.clone()).await?;
        Ok(())
    }

    /// 使用BBO进行撮合
    async fn match_with_bbo(&mut self, bbo: &Bbo) -> Result<()> {
        // 收集需要处理的订单信息
        let mut orders_to_process = Vec::new();

        for (order_id, order) in &self.pending_orders {
            let order_price = order.price.unwrap_or(Price::new(0.0));
            let can_match = match order.side {
                OrderSide::Buy => order_price >= bbo.ask_price,
                OrderSide::Sell => order_price <= bbo.bid_price,
            };

            if can_match {
                orders_to_process.push((order_id.clone(), order.clone()));
            }
        }

        // 处理匹配的订单
        let mut orders_to_remove = Vec::new();

        for (order_id, mut order) in orders_to_process {
            let trade_price = match order.side {
                OrderSide::Buy => bbo.ask_price,
                OrderSide::Sell => bbo.bid_price,
            };

            let available_quantity = match order.side {
                OrderSide::Buy => bbo.ask_quantity,
                OrderSide::Sell => bbo.bid_quantity,
            };

            let trade_quantity = order.quantity.min(available_quantity);
            if trade_quantity > 0.0 {
                // 执行交易
                self.execute_trade(&mut order, trade_price, trade_quantity)
                    .await?;

                // 更新订单状态
                order.quantity -= trade_quantity;
                if order.quantity <= 0.0 {
                    order.status = OrderStatus::Filled;
                    orders_to_remove.push(order_id.clone());
                } else {
                    order.status = OrderStatus::PartiallyFilled;
                }

                // 更新订单到pending_orders
                if let Some(pending_order) = self.pending_orders.get_mut(&order_id) {
                    *pending_order = order.clone();
                }

                // 发送订单更新
                info!("Sending order update: {:?}", order);
                self.send_order_update(order).await?;
            }
        }

        // 移除已完成的订单
        for order_id in orders_to_remove {
            self.pending_orders.remove(&order_id);
            self.orderbook.remove_order(&order_id);
        }

        Ok(())
    }

    /// 撮合待处理订单
    async fn match_pending_orders(&mut self) -> Result<()> {
        // 收集所有待处理订单的ID，避免在迭代时修改HashMap
        let pending_order_ids: Vec<String> = self.pending_orders.keys().cloned().collect();

        for order_id in pending_order_ids {
            if let Some(mut order) = self.pending_orders.get(&order_id).cloned() {
                // 尝试重新撮合每个待处理订单
                match self.orderbook.match_order(&order) {
                    Ok((matches, remaining_quantity)) => {
                        // 处理所有新的成交
                        for (matched_order, trade_price, trade_quantity) in matches {
                            self.execute_trade(&mut order, trade_price, trade_quantity)
                                .await?;

                            // 更新被撮合订单的状态
                            let mut updated_order = matched_order;
                            if updated_order.quantity <= 0.0 {
                                updated_order.status = OrderStatus::Filled;
                                self.pending_orders.remove(&updated_order.id);
                            } else {
                                updated_order.status = OrderStatus::PartiallyFilled;
                                self.pending_orders
                                    .insert(updated_order.id.clone(), updated_order.clone());
                            }
                            self.send_order_update(updated_order).await?;
                        }

                        // 更新原订单状态
                        if let Some(pending_order) = self.pending_orders.get(&order_id).cloned() {
                            let mut updated_order = pending_order;
                            if remaining_quantity <= 0.0 {
                                updated_order.status = OrderStatus::Filled;
                                self.pending_orders.remove(&order_id);
                            } else if remaining_quantity < updated_order.quantity {
                                updated_order.status = OrderStatus::PartiallyFilled;
                                updated_order.quantity = remaining_quantity;
                                self.pending_orders
                                    .insert(order_id.clone(), updated_order.clone());
                            }
                            self.send_order_update(updated_order).await?;
                        }
                    }
                    Err(_) => {
                        // 撮合失败，保持订单在待处理状态
                        continue;
                    }
                }
            }
        }

        Ok(())
    }

    /// 执行交易
    async fn execute_trade(
        &mut self,
        order: &mut Order,
        price: Price,
        quantity: f64,
    ) -> Result<()> {
        let trade = Trade {
            id: format!(
                "{}_{}",
                order.id,
                chrono::Utc::now().timestamp_nanos_opt().unwrap_or(0)
            ),
            symbol: order.symbol.clone(),
            price,
            quantity,
            side: order.side.clone(),
            timestamp: Some(chrono::Utc::now()),
        };

        info!(
            "Trade executed: {} {} {} @ {} (quantity: {})",
            trade.id,
            trade.symbol,
            match trade.side {
                OrderSide::Buy => "BUY",
                OrderSide::Sell => "SELL",
            },
            trade.price,
            trade.quantity
        );

        // 计算手续费（假设0.1%的手续费率）
        let fee_rate = 0.001;
        let commission = quantity * price.value() * fee_rate;

        // 更新订单的执行信息
        if let Some(ref mut exec_info) = order.execution_info {
            exec_info.last_filled_price = Some(price);
            exec_info.last_filled_quantity = quantity;
            exec_info.filled_quantity += quantity;
            exec_info.commission += commission;
            exec_info.trade_id = Some(trade.id.clone());

            // 计算平均价格
            if exec_info.filled_quantity > 0.0 {
                let total_value = exec_info
                    .average_price
                    .map_or(0.0, |p| p.value() * (exec_info.filled_quantity - quantity))
                    + price.value() * quantity;
                exec_info.average_price = Some(Price::new(total_value / exec_info.filled_quantity));
            }
        } else {
            order.execution_info = Some(crate::types::OrderExecutionInfo {
                last_filled_price: Some(price),
                last_filled_quantity: quantity,
                filled_quantity: quantity,
                average_price: Some(price),
                commission,
                commission_asset: "USDT".to_string(),
                trade_id: Some(trade.id.clone()),
            });
        }

        // 更新账户状态
        {
            let mut account_manager = self.account_manager.lock().await;
            if let Err(e) = account_manager.process_trade(trade.clone()) {
                error!("Failed to process trade in account manager: {}", e);
                // 这里不返回错误，因为账户处理失败不应该影响交易的发送
            }
        }

        if let Err(e) = self.trade_tx.send(trade) {
            error!("Failed to send trade: {}", e);
            return Err(BacktestError::Communication(format!(
                "Failed to send trade: {}",
                e
            )));
        }

        Ok(())
    }

    /// 发送订单更新
    async fn send_order_update(&self, order: Order) -> Result<()> {
        if let Err(e) = self.order_update_tx.send(order) {
            error!("Failed to send order update: {}", e);
            return Err(BacktestError::Communication(format!(
                "Failed to send order update: {}",
                e
            )));
        }

        Ok(())
    }

    /// 取消订单
    pub async fn cancel_order(&mut self, order_id: &str) -> Result<()> {
        if let Some(mut order) = self.pending_orders.remove(order_id) {
            order.status = OrderStatus::Cancelled;
            self.orderbook.remove_order(order_id);
            self.send_order_update(order).await?;
            info!("Order cancelled: {}", order_id);
        }

        Ok(())
    }

    /// 获取订单簿快照
    pub fn get_orderbook_snapshot(
        &self,
    ) -> (
        std::collections::BTreeMap<Price, f64>,
        std::collections::BTreeMap<Price, f64>,
    ) {
        self.orderbook.snapshot()
    }

    /// 更新账户管理器的价格信息
    async fn update_account_prices(&mut self, market_data: &MarketData) {
        match market_data {
            MarketData::Bbo(bbo) => {
                // 使用中间价更新价格
                let mid_price = (bbo.bid_price.value() + bbo.ask_price.value()) / 2.0;
                // 假设交易对为BTCUSDT，实际应该从市场数据中获取
                self.update_current_price("BTCUSDT".to_string(), mid_price);
                let mut account_manager = self.account_manager.lock().await;
                account_manager.update_price("BTCUSDT".to_string(), Price::new(mid_price));
            }
            MarketData::BookTicker(bookticker) => {
                let mid_price = bookticker.mid_price().value();
                // 假设交易对为BTCUSDT，实际应该从市场数据中获取
                self.update_current_price("BTCUSDT".to_string(), mid_price);
                let mut account_manager = self.account_manager.lock().await;
                account_manager.update_price("BTCUSDT".to_string(), Price::new(mid_price));
            }
            MarketData::Trade(trade) => {
                self.update_current_price(trade.symbol.clone(), trade.price.value());
                let mut account_manager = self.account_manager.lock().await;
                account_manager.update_price(trade.symbol.clone(), trade.price);
            }
            MarketData::TradeData(trade_data) => {
                self.update_current_price(trade_data.symbol.clone(), trade_data.price.value());
                let mut account_manager = self.account_manager.lock().await;
                account_manager.update_price(trade_data.symbol.clone(), trade_data.price);
            }
            MarketData::OrderBook(_) => {
                // 订单簿快照不包含单一价格，跳过
            }
        }
    }

    /// 获取账户摘要
    pub async fn get_account_summary(&self) -> crate::account::account::AccountSummary {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_account_summary()
    }

    /// 获取账户余额
    pub async fn get_account_balance(&self, asset: &str) -> f64 {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_balance(asset)
    }

    /// 获取账户净值
    pub async fn get_account_net_value(&self) -> f64 {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_net_value()
    }

    /// 获取仓位信息
    pub async fn get_position(&self, symbol: &str) -> Option<crate::account::position::Position> {
        let account_manager = self.account_manager.lock().await;
        account_manager.get_position(symbol).cloned()
    }

    /// 获取当前市场价格
    fn get_current_market_price(&self, symbol: &str) -> Option<f64> {
        self.current_prices.get(symbol).copied()
    }

    /// 更新当前市场价格
    fn update_current_price(&mut self, symbol: String, price: f64) {
        self.current_prices.insert(symbol, price);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::account::types::AccountConfig;
    use crate::types::{OrderStatus, OrderType, TradeData};
    use chrono::Utc;
    use tokio::sync::{broadcast, mpsc};

    fn create_test_account_manager() -> Arc<Mutex<AccountManager>> {
        let config = AccountConfig::default();
        Arc::new(Mutex::new(AccountManager::new(
            "test_account".to_string(),
            config,
        )))
    }

    fn create_test_order(id: &str, side: OrderSide, price: Option<Price>, quantity: f64) -> Order {
        Order {
            id: id.to_string(),
            client_order_id: format!("client_{}", id),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side,
            price,
            quantity,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
            execution_info: None,
        }
    }

    fn create_market_order(id: &str, side: OrderSide, quantity: f64) -> Order {
        Order {
            id: id.to_string(),
            client_order_id: format!("client_{}", id),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Market,
            side,
            price: None,
            quantity,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
            execution_info: None,
        }
    }

    #[tokio::test]
    async fn test_matching_engine_limit_order_matching() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let mut engine = MatchingEngine::new(
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 10.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 创建买单进行撮合
        let mut buy_order = create_test_order("buy1", OrderSide::Buy, Some(Price::new(100.0)), 5.0);

        // 执行撮合
        let result = engine.match_limit_order(&mut buy_order).await;
        assert!(result.is_ok());

        // 验证买单状态
        assert_eq!(buy_order.status, OrderStatus::Filled);

        // 验证是否有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_ok());
        let trade = trade.unwrap();
        assert_eq!(trade.quantity, 5.0);
        assert_eq!(trade.price, Price::new(100.0));

        // 验证订单更新
        let order_update = order_update_rx.try_recv();
        assert!(order_update.is_ok());
    }

    #[tokio::test]
    async fn test_matching_engine_market_order() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let mut engine = MatchingEngine::new(
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 10.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 创建市价买单
        let mut market_buy_order = create_market_order("buy1", OrderSide::Buy, 3.0);

        // 执行撮合
        let result = engine.match_market_order(&mut market_buy_order).await;
        assert!(result.is_ok());

        // 验证市价单状态
        assert_eq!(market_buy_order.status, OrderStatus::Filled);

        // 验证是否有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_ok());
        let trade = trade.unwrap();
        assert_eq!(trade.quantity, 3.0);
        assert_eq!(trade.price, Price::new(100.0));
    }

    #[tokio::test]
    async fn test_matching_engine_partial_fill() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, mut trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let mut engine = MatchingEngine::new(
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加小的卖单到订单簿
        let sell_order = create_test_order("sell1", OrderSide::Sell, Some(Price::new(100.0)), 3.0);
        engine.orderbook.add_order(sell_order.clone());
        engine
            .pending_orders
            .insert("sell1".to_string(), sell_order);

        // 创建大的买单进行撮合
        let mut buy_order =
            create_test_order("buy1", OrderSide::Buy, Some(Price::new(100.0)), 10.0);

        // 执行撮合
        let result = engine.match_limit_order(&mut buy_order).await;
        assert!(result.is_ok());

        // 验证买单状态（应该是部分成交）
        assert_eq!(buy_order.status, OrderStatus::PartiallyFilled);
        assert_eq!(buy_order.quantity, 7.0); // 剩余7.0未成交

        // 验证是否有交易产生
        let trade = trade_rx.try_recv();
        assert!(trade.is_ok());
        let trade = trade.unwrap();
        assert_eq!(trade.quantity, 3.0); // 成交3.0

        // 验证买单被加入到待处理订单
        assert!(engine.pending_orders.contains_key("buy1"));
    }

    #[tokio::test]
    async fn test_matching_engine_cancel_order() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let mut engine = MatchingEngine::new(
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 添加订单到待处理列表
        let order = create_test_order("test1", OrderSide::Buy, Some(Price::new(99.0)), 10.0);
        engine
            .pending_orders
            .insert("test1".to_string(), order.clone());
        engine.orderbook.add_order(order);

        // 取消订单
        let result = engine.cancel_order("test1").await;
        assert!(result.is_ok());

        // 验证订单被移除
        assert!(!engine.pending_orders.contains_key("test1"));

        // 验证订单更新通知
        let order_update = order_update_rx.try_recv();
        assert!(order_update.is_ok());
        let updated_order = order_update.unwrap();
        assert_eq!(updated_order.status, OrderStatus::Cancelled);
    }

    #[tokio::test]
    async fn test_time_sync_buffer() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, mut market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let mut engine = MatchingEngine::new(
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 设置较大的时间窗口，确保数据不会被自动处理
        engine.set_time_window_micros(1_000_000_000); // 1000秒

        // 创建不同时间戳的市场数据
        let base_timestamp = 1640995200000000u64; // 微秒级时间戳

        // 创建 BBO 数据（较新的时间戳）
        let bbo = Bbo {
            update_id: 1,
            bid_price: Price::new(99.5),
            bid_quantity: 10.0,
            ask_price: Price::new(100.5),
            ask_quantity: 15.0,
            timestamp: Some(base_timestamp + 2000), // 较新
        };

        // 创建 TradeData（较老的时间戳）
        let trade_data = TradeData {
            exchange: "binance".to_string(),
            symbol: "BTCUSDT".to_string(),
            timestamp: base_timestamp + 1000, // 较老
            local_timestamp: base_timestamp + 1000,
            id: "trade1".to_string(),
            side: OrderSide::Buy,
            price: Price::new(100.0),
            amount: 1.0,
        };

        // 先发送较新的 BBO 数据（只添加到缓冲区）
        let result = engine.add_to_buffer_only(MarketData::Bbo(bbo)).await;
        assert!(result.is_ok());

        // 再发送较老的 TradeData（只添加到缓冲区）
        let result = engine
            .add_to_buffer_only(MarketData::TradeData(trade_data))
            .await;
        assert!(result.is_ok());

        // 验证缓冲区中有数据
        assert_eq!(engine.time_sync_buffer.len(), 2);

        // 强制处理缓冲区数据
        let result = engine.process_buffered_data().await;
        assert!(result.is_ok());

        // 验证缓冲区已清空
        assert_eq!(engine.time_sync_buffer.len(), 0);

        // 验证数据按时间戳顺序转发（应该先收到 TradeData，再收到 BBO）
        let first_data = market_data_forward_rx.try_recv();
        assert!(first_data.is_ok());
        match first_data.unwrap() {
            MarketData::TradeData(td) => {
                assert_eq!(td.id, "trade1");
                assert_eq!(td.timestamp, base_timestamp + 1000);
            }
            _ => panic!("Expected TradeData first"),
        }

        let second_data = market_data_forward_rx.try_recv();
        assert!(second_data.is_ok());
        match second_data.unwrap() {
            MarketData::Bbo(bbo) => {
                assert_eq!(bbo.timestamp, Some(base_timestamp + 2000));
            }
            _ => panic!("Expected BBO second"),
        }
    }

    #[tokio::test]
    async fn test_time_sync_buffer_overflow() {
        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let mut engine = MatchingEngine::new(
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 设置较小的缓冲区限制用于测试
        engine.buffer_size_limit = 5;

        let base_timestamp = 1640995200000000u64;

        // 添加超过限制的数据
        for i in 0..10 {
            let bbo = Bbo {
                update_id: i,
                bid_price: Price::new(99.5),
                bid_quantity: 10.0,
                ask_price: Price::new(100.5),
                ask_quantity: 15.0,
                timestamp: Some(base_timestamp + i * 1000),
            };

            let result = engine.process_market_data(MarketData::Bbo(bbo)).await;
            assert!(result.is_ok());
        }

        // 验证缓冲区大小不超过限制
        assert!(engine.time_sync_buffer.len() <= engine.buffer_size_limit);
    }

    #[tokio::test]
    async fn test_playback_rate_control() {
        use crate::config::PlaybackConfig;

        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();

        // 创建回放配置：每秒5条，批处理2条
        let playback_config = PlaybackConfig {
            rate_per_second: 5,
            enabled: true,
            batch_size: 2,
        };

        let mut engine = MatchingEngine::new_with_playback_config(
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
            playback_config,
        );

        let base_timestamp = 1640995200000000u64;

        // 添加多条数据到缓冲区
        for i in 0..10 {
            let bbo = Bbo {
                update_id: i,
                bid_price: Price::new(99.5),
                bid_quantity: 10.0,
                ask_price: Price::new(100.5),
                ask_quantity: 15.0,
                timestamp: Some(base_timestamp + i * 1000),
            };

            let result = engine.add_to_buffer_only(MarketData::Bbo(bbo)).await;
            assert!(result.is_ok());
        }

        // 验证缓冲区中有数据
        assert_eq!(engine.time_sync_buffer.len(), 10);

        // 记录开始时间
        let start_time = Instant::now();

        // 强制处理缓冲区数据（应该受到速率控制）
        let result = engine.process_buffered_data().await;
        assert!(result.is_ok());

        // 记录结束时间
        let elapsed = start_time.elapsed();

        // 验证缓冲区已清空
        assert_eq!(engine.time_sync_buffer.len(), 0);

        // 验证处理时间：10条数据，每秒5条，应该至少需要1秒多一点
        // 但由于批处理和测试环境的不确定性，我们只检查是否有明显的延迟
        println!("Processing 10 items took: {:?}", elapsed);

        // 验证回放配置
        assert_eq!(engine.get_playback_config().rate_per_second, 5);
        assert_eq!(engine.get_playback_config().batch_size, 2);
        assert!(engine.get_playback_config().enabled);
    }

    #[tokio::test]
    async fn test_playback_config_update() {
        use crate::config::PlaybackConfig;

        // 创建通道
        let (_market_data_tx, market_data_rx) = broadcast::channel(100);
        let (_order_tx, order_rx) = mpsc::channel(100);
        let (trade_tx, _trade_rx) = broadcast::channel(100);
        let (order_update_tx, _order_update_rx) = broadcast::channel(100);
        let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

        let account_manager = create_test_account_manager();
        let mut engine = MatchingEngine::new(
            account_manager,
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        );

        // 验证默认配置
        let default_config = engine.get_playback_config();
        assert_eq!(default_config.rate_per_second, 1000);
        assert!(default_config.enabled);
        assert_eq!(default_config.batch_size, 10);

        // 更新配置
        let new_config = PlaybackConfig {
            rate_per_second: 500,
            enabled: false,
            batch_size: 5,
        };

        engine.update_playback_config(new_config.clone());

        // 验证配置已更新
        let updated_config = engine.get_playback_config();
        assert_eq!(updated_config.rate_per_second, 500);
        assert!(!updated_config.enabled);
        assert_eq!(updated_config.batch_size, 5);
    }
}
